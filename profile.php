<?php
// profile.php
// Trang giới thiệu bản thân (tĩnh)

// Thông tin cá nhân
$profile = [
    'name'   => 'Lâm Văn Na',
    'dob'    => '09/02/2003',
    'gender' => 'Nam',
    'class'  => 'DA22TTB',
    'email'  => '<EMAIL>',
   
    'avatar' => 'uploads/lvn.jpg'
];
?>
<!doctype html>
<html lang="vi">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Giớ<PERSON> thiệu bản thân - <?php echo $profile['name']; ?></title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #74ebd5 0%, #ACB6E5 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
    .card {
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
      max-width: 600px;
      padding: 30px;
      text-align: center;
    }
    .avatar {
      width: 180px;
      height: 180px;
      border-radius: 50%;
      object-fit: cover;
      border: 5px solid #eee;
      margin-bottom: 20px;
    }
    h1 {
      margin: 10px 0;
      font-size: 26px;
      color: #333;
    }
    .info {
      text-align: left;
      margin-top: 20px;
      font-size: 16px;
      line-height: 1.8;
    }
    .info p {
      margin: 8px 0;
    }
    .info strong {
      display: inline-block;
      width: 140px;
      color: #555;
    }
  </style>
</head>
<body>
  <div class="card">
    <img src="<?php echo $profile['avatar']; ?>" alt="Avatar" class="avatar">
    <h1><?php echo $profile['name']; ?></h1>
    <div class="info">
      <p><strong>Họ và tên:</strong> <?php echo $profile['name']; ?></p>
      <p><strong>Ngày sinh:</strong> <?php echo $profile['dob']; ?></p>
      <p><strong>Giới tính:</strong> <?php echo $profile['gender']; ?></p>
      <p><strong>Lớp:</strong> <?php echo $profile['class']; ?></p>
      <p><strong>Email:</strong> <?php echo $profile['email']; ?></p>
    </div>
  </div>
</body>
</html>
